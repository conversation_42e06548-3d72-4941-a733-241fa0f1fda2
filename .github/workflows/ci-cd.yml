name: BiteBase Intelligence CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'
  REDIS_VERSION: '7'

jobs:
  # Security and Code Quality Checks
  security-scan:
    name: Security & Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install security tools
        run: |
          pip install safety bandit semgrep
          npm install -g audit-ci

      - name: Run dependency security scan
        run: |
          cd backend
          pip install -r requirements.txt
          safety check --json --output safety-report.json || true
          
      - name: Run static security analysis
        run: |
          cd backend
          bandit -r app/ -f json -o bandit-report.json || true
          
      - name: Run Semgrep security scan
        run: |
          semgrep --config=auto --json --output=semgrep-report.json . || true

      - name: Frontend security audit
        run: |
          cd frontend
          npm ci
          npm audit --audit-level=moderate || true
          audit-ci --moderate || true

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        with:
          name: security-reports
          path: |
            *-report.json
            frontend/npm-audit.json

  # Backend Testing
  backend-test:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache Python dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install backend dependencies
        run: |
          cd backend
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-asyncio httpx

      - name: Set up test environment
        run: |
          cd backend
          cp .env.example .env.test
          echo "DATABASE_URL=postgresql://testuser:testpassword@localhost:5432/testdb" >> .env.test
          echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
          echo "TESTING=true" >> .env.test

      - name: Run database migrations
        run: |
          cd backend
          export $(cat .env.test | xargs)
          python -m alembic upgrade head

      - name: Run backend tests
        run: |
          cd backend
          export $(cat .env.test | xargs)
          pytest tests/ -v --cov=app --cov-report=xml --cov-report=html

      - name: Upload backend coverage
        uses: codecov/codecov-action@v3
        with:
          file: backend/coverage.xml
          flags: backend
          name: backend-coverage

  # Frontend Testing
  frontend-test:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install frontend dependencies
        run: |
          cd frontend
          npm ci

      - name: Run frontend linting
        run: |
          cd frontend
          npm run lint

      - name: Run frontend type checking
        run: |
          cd frontend
          npm run type-check

      - name: Run frontend tests
        run: |
          cd frontend
          npm run test:coverage

      - name: Upload frontend coverage
        uses: codecov/codecov-action@v3
        with:
          file: frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # Integration Testing
  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: |
          cd backend && pip install -r requirements.txt
          cd ../frontend && npm ci

      - name: Build frontend
        run: |
          cd frontend
          npm run build

      - name: Start backend server
        run: |
          cd backend
          export DATABASE_URL=postgresql://testuser:testpassword@localhost:5432/testdb
          export REDIS_URL=redis://localhost:6379/0
          python -m alembic upgrade head
          uvicorn app.main:app --host 0.0.0.0 --port 8000 &
          sleep 10

      - name: Run integration tests
        run: |
          cd backend
          python scripts/integration_tests.py

      - name: Run load tests
        run: |
          cd backend
          python scripts/load_testing.py --users 5 --duration 30

  # Build and Push Docker Images
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [security-scan, integration-test]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/${{ github.repository }}/backend
            ghcr.io/${{ github.repository }}/frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: ghcr.io/${{ github.repository }}/backend:${{ github.sha }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: ghcr.io/${{ github.repository }}/frontend:${{ github.sha }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add your staging deployment commands here
          # This could be Kubernetes, Docker Compose, or cloud provider specific

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add your production deployment commands here

      - name: Run post-deployment tests
        run: |
          echo "Running post-deployment verification..."
          # Add health checks and smoke tests

      - name: Notify deployment
        if: always()
        run: |
          echo "Sending deployment notification..."
          # Add notification logic (Slack, email, etc.)

  # Performance Testing (on main branch)
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install load testing tools
        run: |
          pip install locust aiohttp

      - name: Run performance tests
        run: |
          cd scripts
          python load_testing.py --scenarios --base-url ${{ secrets.STAGING_URL }}

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: load_test_results.json
