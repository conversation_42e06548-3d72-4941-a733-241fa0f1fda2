name: "BiteBase Intelligence Enhancement: Complete 4P Framework Implementation"
description: |

## Purpose
Complete the transformation of BiteBase Intelligence from a solid analytics foundation into a comprehensive 4P framework (Product, Place, Price, Promotion) platform that empowers restaurant owners with actionable insights, automated recommendations, and predictive intelligence.

## Core Principles
1. **Evidence-Based Analytics**: All insights backed by real data and measurable outcomes
2. **Progressive Enhancement**: Build on existing foundation while adding advanced capabilities
3. **User-Centric Design**: Prioritize restaurant owner needs and workflow optimization
4. **Performance-First**: Maintain sub-3s load times and real-time responsiveness

---

## Goal
Transform BiteBase Intelligence into a market-leading restaurant intelligence platform by implementing missing 4P framework components: advanced menu engineering, customer density analytics, revenue forecasting, and marketing automation.

## Why
- **Business value**: Addresses $15B+ restaurant technology market with AI-driven insights
- **User demand**: High feedback for food cost analysis, forecasting, and customer segmentation
- **Competitive advantage**: Comprehensive 4P integration vs. fragmented point solutions
- **Revenue impact**: 25% projected increase in subscription upgrades

## What
A comprehensive enhancement that adds:
- Product Intelligence: Menu engineering with cost analysis and dynamic pricing
- Place Intelligence: Customer density heatmaps and site selection tools
- Price Intelligence: ML-based revenue forecasting and spending analytics
- Promotion Intelligence: Advanced customer segmentation and marketing automation

### Success Criteria
- [ ] Menu engineering dashboard with 90%+ classification accuracy
- [ ] Customer density heatmaps with real-time updates
- [ ] Revenue forecasting with 85%+ accuracy for 30-day predictions
- [ ] Customer segmentation with automated campaign generation
- [ ] 70%+ feature adoption by Professional+ tier users within 3 months
- [ ] 15% average improvement in profit margins for users

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Critical technical documentation
- file: BITEBASE_INTELLIGENCE_ENHANCEMENT_PRD.md
  why: Complete feature gap analysis and business requirements
  
- file: BITEBASE_WEB_FLOW_DESIGN.md
  why: Detailed UI/UX design and component specifications
  
- file: IMPLEMENTATION_ROADMAP_SUMMARY.md
  why: Phase-by-phase implementation timeline and priorities
  
- file: BUILD_STATUS_REPORT.md
  why: Current build status and resolved/remaining issues
  
- file: backend/app/main.py
  why: Current FastAPI structure and existing endpoints
  
- file: frontend/src/app/layout.tsx
  why: Next.js 15 app structure and navigation patterns
  
- file: backend/app/models/
  why: Existing database models and relationships
  
- file: frontend/src/components/dashboard/
  why: Current dashboard component patterns and architecture
```

### Current Codebase Structure
```bash
# Backend (FastAPI + SQLAlchemy + PostgreSQL)
backend/
├── app/
│   ├── main.py                     # FastAPI application entry
│   ├── api/v1/endpoints/           # Existing API endpoints
│   ├── models/                     # SQLAlchemy models ✅
│   ├── schemas/                    # Pydantic schemas ✅
│   ├── services/                   # Business logic services ✅
│   │   ├── nl_query/              # Natural language processing
│   │   ├── insights/              # Automated insights engine
│   │   ├── collaboration/         # Real-time collaboration
│   │   └── connectors/            # Data connector framework
│   └── core/                      # Core utilities and config
└── tests/                         # Pytest test suite

# Frontend (Next.js 15 + TypeScript + Tailwind)
frontend/
├── src/
│   ├── app/                       # Next.js app router ✅
│   ├── components/                # React components ✅
│   │   ├── dashboard/             # Dashboard components ✅
│   │   ├── nl-query/             # Natural language interface ✅
│   │   ├── collaboration/        # Real-time features ✅
│   │   └── shared/               # Shared UI components ✅
│   ├── lib/                      # Utilities and API client ✅
│   └── types/                    # TypeScript definitions ✅
```

### Target Enhancement Structure
```bash
# New backend services to be added
backend/app/services/
├── product/                       # Product Intelligence (NEW)
│   ├── menu_engineering.py       # Menu performance classification
│   ├── food_cost_analyzer.py     # Cost breakdown and tracking
│   ├── pricing_optimizer.py      # Dynamic pricing algorithms
│   └── seasonal_analyzer.py      # Seasonal trend analysis
├── place/                        # Place Intelligence (NEW)
│   ├── density_analyzer.py       # Customer density heatmaps
│   ├── site_selector.py          # Site selection algorithms
│   └── hotspot_detector.py       # Delivery/pickup optimization
├── price/                        # Price Intelligence (NEW)
│   ├── forecasting_engine.py     # ML revenue forecasting
│   ├── spending_analyzer.py      # Customer spending patterns
│   └── elasticity_calculator.py  # Price elasticity analysis
└── promotion/                    # Promotion Intelligence (NEW)
    ├── segmentation_engine.py    # Customer segmentation
    ├── campaign_generator.py     # Automated campaign creation
    └── loyalty_analyzer.py       # Loyalty program analytics

# New frontend components to be added
frontend/src/components/
├── product/                      # Product Intelligence UI (NEW)
│   ├── MenuEngineeringDashboard.tsx
│   ├── FoodCostAnalyzer.tsx
│   └── DynamicPricingEngine.tsx
├── place/                       # Place Intelligence UI (NEW)
│   ├── CustomerDensityMap.tsx
│   ├── SiteSelectionWizard.tsx
│   └── HotspotAnalysis.tsx
├── price/                       # Price Intelligence UI (NEW)
│   ├── RevenueForecastDashboard.tsx
│   ├── SpendingAnalytics.tsx
│   └── ForecastAccuracy.tsx
└── promotion/                   # Promotion Intelligence UI (NEW)
    ├── CustomerSegmentation.tsx
    ├── CampaignBuilder.tsx
    └── LoyaltyDashboard.tsx
```

### Known Issues & Technical Debt
```python
# RESOLVED: Backend import issues fixed in BUILD_STATUS_REPORT.md
# RESOLVED: SQLAlchemy metadata conflict resolved
# RESOLVED: Missing connector management module created
# RESOLVED: JWT import error fixed

# REMAINING: Frontend missing components need creation
MISSING_COMPONENTS = [
    "components/landing/FoodInspiredLandingPage",
    "components/dashboard/FoodThemeDashboard", 
    "components/ai/EnhancedFloatingChatbot",
    "components/animations/AnimatedButton",
    "components/animations/FoodParticles"
]

# CRITICAL: Database schema extensions needed for 4P framework
# CRITICAL: New API endpoints must follow existing v1 structure pattern
# CRITICAL: Frontend components must use existing TanStack Query patterns
# CRITICAL: All new services must include comprehensive Pytest coverage
```

## Implementation Blueprint

### Data Models and Structure

```python
# models/product.py - Product Intelligence data structures
from sqlalchemy import Column, String, Integer, Decimal, DateTime, UUID, Text, Boolean
from sqlalchemy.dialects.postgresql import JSONB
from app.models.base import BaseModel

class IngredientCost(BaseModel):
    __tablename__ = "ingredient_costs"
    
    ingredient_name = Column(String(255), nullable=False)
    cost_per_unit = Column(Decimal(10,4), nullable=False)
    unit_type = Column(String(50), nullable=False)
    supplier_id = Column(UUID(as_uuid=True), nullable=True)
    restaurant_id = Column(UUID(as_uuid=True), nullable=False)
    last_updated = Column(DateTime, nullable=False)

class MenuItemCost(BaseModel):
    __tablename__ = "menu_item_costs"
    
    menu_item_id = Column(UUID(as_uuid=True), nullable=False)
    ingredient_cost = Column(Decimal(10,2), nullable=False)
    labor_cost = Column(Decimal(10,2), nullable=False)
    overhead_cost = Column(Decimal(10,2), nullable=False)
    total_cost = Column(Decimal(10,2), nullable=False)
    profit_margin = Column(Decimal(5,2), nullable=False)
    classification = Column(String(20), nullable=True)  # star, dog, plow_horse, puzzle

class PricingHistory(BaseModel):
    __tablename__ = "pricing_history"
    
    menu_item_id = Column(UUID(as_uuid=True), nullable=False)
    old_price = Column(Decimal(10,2), nullable=False)
    new_price = Column(Decimal(10,2), nullable=False)
    reason = Column(String(255), nullable=True)
    effective_date = Column(DateTime, nullable=False)
    created_by = Column(UUID(as_uuid=True), nullable=False)

# models/place.py - Place Intelligence data structures
class CustomerLocation(BaseModel):
    __tablename__ = "customer_locations"
    
    customer_id = Column(UUID(as_uuid=True), nullable=True)
    latitude = Column(Decimal(10,8), nullable=False)
    longitude = Column(Decimal(11,8), nullable=False)
    visit_frequency = Column(Integer, nullable=False, default=1)
    last_visit = Column(DateTime, nullable=False)
    anonymized_id = Column(String(255), nullable=False)

class SiteScore(BaseModel):
    __tablename__ = "site_scores"
    
    latitude = Column(Decimal(10,8), nullable=False)
    longitude = Column(Decimal(11,8), nullable=False)
    overall_score = Column(Decimal(3,2), nullable=False)
    demographic_score = Column(Decimal(3,2), nullable=False)
    competition_score = Column(Decimal(3,2), nullable=False)
    accessibility_score = Column(Decimal(3,2), nullable=False)
    cost_score = Column(Decimal(3,2), nullable=False)
    analysis_date = Column(DateTime, nullable=False)

# models/price.py - Price Intelligence data structures  
class ForecastModel(BaseModel):
    __tablename__ = "forecast_models"
    
    restaurant_id = Column(UUID(as_uuid=True), nullable=False)
    model_type = Column(String(50), nullable=False)
    model_parameters = Column(JSONB, nullable=False)
    accuracy_metrics = Column(JSONB, nullable=False)
    training_data_period = Column(String(100), nullable=False)
    last_trained = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)

class RevenueForecast(BaseModel):
    __tablename__ = "revenue_forecasts"
    
    restaurant_id = Column(UUID(as_uuid=True), nullable=False)
    forecast_date = Column(DateTime, nullable=False)
    predicted_revenue = Column(Decimal(12,2), nullable=False)
    confidence_interval_lower = Column(Decimal(12,2), nullable=False)
    confidence_interval_upper = Column(Decimal(12,2), nullable=False)
    actual_revenue = Column(Decimal(12,2), nullable=True)
    model_id = Column(UUID(as_uuid=True), nullable=False)

# models/promotion.py - Promotion Intelligence data structures
class CustomerSegment(BaseModel):
    __tablename__ = "customer_segments"
    
    segment_name = Column(String(255), nullable=False)
    segment_criteria = Column(JSONB, nullable=False)
    rfm_score_range = Column(JSONB, nullable=False)
    customer_count = Column(Integer, nullable=False, default=0)
    average_clv = Column(Decimal(12,2), nullable=False, default=0)

class CustomerSegmentAssignment(BaseModel):
    __tablename__ = "customer_segment_assignments"
    
    customer_id = Column(UUID(as_uuid=True), nullable=False)
    segment_id = Column(UUID(as_uuid=True), nullable=False)
    assignment_date = Column(DateTime, nullable=False)
    confidence_score = Column(Decimal(3,2), nullable=False)
    is_active = Column(Boolean, default=True)
```

### List of Tasks to be Completed

```yaml
Phase 1: Product Intelligence Foundation (3-4 weeks)
CREATE backend/app/models/product.py:
  - PATTERN: Follow existing BaseModel pattern in app/models/
  - Add IngredientCost, MenuItemCost, PricingHistory models
  - Include proper relationships and constraints
  
CREATE backend/app/services/product/menu_engineering.py:
  - PATTERN: Follow existing service pattern in app/services/
  - Implement Star/Dog/Plow Horse/Puzzle classification algorithm
  - Calculate menu item performance metrics
  - Include comprehensive error handling

CREATE backend/app/services/product/food_cost_analyzer.py:
  - PATTERN: Async service methods like existing services
  - Calculate total food costs with ingredient breakdown
  - Track cost trends and variance analysis
  - Support multiple cost calculation methods

CREATE backend/app/api/v1/endpoints/product_intelligence.py:
  - PATTERN: Follow existing endpoint structure in api/v1/endpoints/
  - Implement /menu-engineering/, /food-costs/, /pricing/ routes
  - Use proper Pydantic schemas for request/response
  - Include rate limiting and authentication

CREATE frontend/src/components/product/MenuEngineeringDashboard.tsx:
  - PATTERN: Follow existing dashboard pattern in components/dashboard/
  - Use TanStack Query for data fetching
  - Implement responsive grid layout with Tailwind
  - Include real-time updates and error states

Phase 2: Place Intelligence Enhancement (3-4 weeks)
CREATE backend/app/models/place.py:
  - Add CustomerLocation, SiteScore, MarketAnalysis models
  - Include geospatial indexing for performance
  - Privacy-compliant customer tracking
  
CREATE backend/app/services/place/density_analyzer.py:
  - PATTERN: Use PostGIS extensions for geospatial queries
  - Implement customer density heatmap algorithms
  - Calculate traffic patterns and hotspot detection
  - Include privacy anonymization

CREATE frontend/src/components/place/CustomerDensityMap.tsx:
  - PATTERN: Extend existing map components if available
  - Use Leaflet or Mapbox for interactive mapping
  - Implement heatmap overlay visualization
  - Add real-time density updates

Phase 3: Price Intelligence ML Implementation (4-5 weeks)
CREATE backend/app/models/price.py:
  - Add ForecastModel, RevenueForecast, CustomerSpending models
  - Include model versioning and accuracy tracking
  - Support multiple forecasting algorithms
  
CREATE backend/app/services/price/forecasting_engine.py:
  - PATTERN: Use scikit-learn or similar ML library
  - Implement ARIMA, Prophet, or LSTM models
  - Include model validation and retraining
  - Add confidence interval calculations

CREATE frontend/src/components/price/RevenueForecastDashboard.tsx:
  - PATTERN: Use Chart.js or similar for visualizations
  - Display forecasts with confidence intervals
  - Include model accuracy metrics
  - Add forecast export functionality

Phase 4: Promotion Intelligence Automation (3-4 weeks)  
CREATE backend/app/models/promotion.py:
  - Add CustomerSegment, SegmentAssignment, AutomatedCampaign models
  - Support RFM scoring and behavioral segmentation
  - Include campaign performance tracking
  
CREATE backend/app/services/promotion/segmentation_engine.py:
  - PATTERN: Use clustering algorithms (K-means, DBSCAN)
  - Implement RFM analysis with automated scoring
  - Calculate customer lifetime value
  - Include segment migration tracking

CREATE frontend/src/components/promotion/CustomerSegmentationDashboard.tsx:
  - PATTERN: Interactive segment visualization
  - Drag-and-drop segment builder interface
  - RFM matrix visualization
  - Segment performance metrics
```

### Per Task Pseudocode

```python
# Phase 1: Menu Engineering Service
class MenuEngineeringService:
    async def classify_menu_items(self, restaurant_id: UUID) -> List[MenuClassification]:
        # PATTERN: Follow existing service async patterns
        menu_items = await self.db.query(MenuItem).filter(
            MenuItem.restaurant_id == restaurant_id
        ).all()
        
        classifications = []
        for item in menu_items:
            # Calculate performance metrics
            sales_volume = await self.calculate_sales_volume(item.id)
            profit_margin = await self.calculate_profit_margin(item.id)
            
            # Apply Boston Consulting Matrix logic
            if sales_volume > median_sales and profit_margin > median_margin:
                classification = "star"
            elif sales_volume > median_sales and profit_margin <= median_margin:
                classification = "plow_horse"
            elif sales_volume <= median_sales and profit_margin > median_margin:
                classification = "puzzle"
            else:
                classification = "dog"
                
            classifications.append(MenuClassification(
                item_id=item.id,
                classification=classification,
                sales_volume=sales_volume,
                profit_margin=profit_margin
            ))
            
        return classifications

# Phase 2: Customer Density Analysis
class DensityAnalyzer:
    async def generate_heatmap_data(
        self, restaurant_id: UUID, radius_km: float = 5.0
    ) -> HeatmapData:
        # PATTERN: Use PostGIS for geospatial queries
        restaurant = await self.get_restaurant_location(restaurant_id)
        
        # Query customer locations within radius
        query = """
        SELECT latitude, longitude, visit_frequency
        FROM customer_locations 
        WHERE ST_DWithin(
            ST_Point(longitude, latitude)::geography,
            ST_Point(%s, %s)::geography,
            %s * 1000  -- Convert km to meters
        )
        """
        
        locations = await self.db.execute(
            query, [restaurant.longitude, restaurant.latitude, radius_km]
        )
        
        # Generate density grid
        grid_data = self.generate_density_grid(locations.fetchall())
        
        return HeatmapData(
            center_lat=restaurant.latitude,
            center_lng=restaurant.longitude,
            radius_km=radius_km,
            density_points=grid_data
        )

# Phase 3: Revenue Forecasting Engine  
class ForecastingEngine:
    async def generate_forecast(
        self, restaurant_id: UUID, days_ahead: int = 30
    ) -> RevenueForecast:
        # PATTERN: Use existing ML patterns if any, or create new
        historical_data = await self.get_historical_revenue(restaurant_id)
        
        # Prepare time series data
        df = pd.DataFrame(historical_data)
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        
        # Choose forecasting model based on data characteristics
        if len(df) > 365:  # Use advanced model for sufficient data
            model = Prophet(
                daily_seasonality=True,
                weekly_seasonality=True,
                yearly_seasonality=True
            )
        else:  # Use simpler model for limited data
            model = ARIMA(order=(1,1,1))
            
        # Train model and generate predictions
        model.fit(df['revenue'])
        forecast = model.predict(periods=days_ahead)
        
        # Calculate confidence intervals
        confidence_intervals = self.calculate_confidence_intervals(forecast)
        
        return RevenueForecast(
            restaurant_id=restaurant_id,
            predictions=forecast.tolist(),
            confidence_lower=confidence_intervals['lower'].tolist(),
            confidence_upper=confidence_intervals['upper'].tolist(),
            model_accuracy=model.accuracy_score()
        )

# Phase 4: Customer Segmentation Engine
class SegmentationEngine:
    async def perform_rfm_analysis(self, restaurant_id: UUID) -> RFMAnalysis:
        # PATTERN: Follow existing analytics patterns
        # Calculate Recency, Frequency, Monetary values
        rfm_query = """
        WITH customer_metrics AS (
            SELECT 
                customer_id,
                EXTRACT(DAYS FROM NOW() - MAX(order_date)) as recency,
                COUNT(*) as frequency,
                SUM(total_amount) as monetary
            FROM orders 
            WHERE restaurant_id = %s
            GROUP BY customer_id
        )
        SELECT * FROM customer_metrics
        """
        
        rfm_data = await self.db.execute(rfm_query, [restaurant_id])
        df = pd.DataFrame(rfm_data.fetchall())
        
        # Apply RFM scoring (1-5 scale)
        df['R_Score'] = pd.qcut(df['recency'], 5, labels=[5,4,3,2,1])
        df['F_Score'] = pd.qcut(df['frequency'].rank(method='first'), 5, labels=[1,2,3,4,5])
        df['M_Score'] = pd.qcut(df['monetary'], 5, labels=[1,2,3,4,5])
        
        # Calculate combined RFM score
        df['RFM_Score'] = df['R_Score'].astype(str) + df['F_Score'].astype(str) + df['M_Score'].astype(str)
        
        # Define segments based on RFM scores
        segment_map = {
            '555': 'Champions',
            '554': 'Loyal Customers', 
            '544': 'Potential Loyalists',
            '511': 'New Customers',
            '155': 'At Risk',
            '144': 'Cannot Lose Them',
            '111': 'Lost'
        }
        
        df['Segment'] = df['RFM_Score'].map(segment_map).fillna('Others')
        
        return RFMAnalysis(
            total_customers=len(df),
            segments=df.groupby('Segment').size().to_dict(),
            rfm_data=df.to_dict('records')
        )
```

### Integration Points
```yaml
ENVIRONMENT:
  - add to: backend/.env
  - vars: |
      # Database Configuration
      DATABASE_URL=postgresql://localhost:5432/bitebase_intelligence
      
      # ML Model Configuration  
      ML_MODEL_PATH=./models/
      FORECAST_MODEL_TYPE=prophet
      
      # External APIs
      MAPBOX_API_KEY=pk.your_mapbox_token
      GEOSPATIAL_SERVICE_URL=https://api.mapbox.com
      
      # Performance Settings
      CACHE_TTL=300
      MAX_FORECAST_DAYS=365
      HEATMAP_GRID_SIZE=50
      
CONFIG:
  - PostgreSQL Extensions: Enable PostGIS for geospatial queries
  - Redis Cache: Configure for real-time data caching
  - ML Dependencies: Install scikit-learn, pandas, Prophet
  
DEPENDENCIES:
  - Update requirements.txt with:
    - scikit-learn>=1.3.0
    - prophet>=1.1.0
    - pandas>=2.0.0
    - postgis>=3.0.0
    - redis>=4.0.0
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
cd backend
black . --check              # Code formatting
flake8 .                     # Style and syntax checking  
mypy app/                    # Type checking

cd ../frontend
npm run lint                 # ESLint checking
npm run type-check          # TypeScript checking

# Expected: No errors. If errors, READ and fix.
```

### Level 2: Unit Tests
```python
# test_menu_engineering.py
async def test_menu_classification():
    """Test menu item classification algorithm"""
    service = MenuEngineeringService()
    result = await service.classify_menu_items(restaurant_id)
    
    assert len(result) > 0
    assert all(item.classification in ['star', 'dog', 'plow_horse', 'puzzle'] 
              for item in result)
    assert all(item.profit_margin >= 0 for item in result)

async def test_food_cost_calculation():
    """Test food cost calculation accuracy"""
    analyzer = FoodCostAnalyzer()
    cost_breakdown = await analyzer.calculate_item_cost(menu_item_id)
    
    assert cost_breakdown.total_cost > 0
    assert cost_breakdown.ingredient_cost + cost_breakdown.labor_cost + cost_breakdown.overhead_cost == cost_breakdown.total_cost

# test_forecasting_engine.py  
async def test_revenue_forecasting():
    """Test revenue forecasting accuracy"""
    engine = ForecastingEngine()
    forecast = await engine.generate_forecast(restaurant_id, days_ahead=30)
    
    assert len(forecast.predictions) == 30
    assert all(pred > 0 for pred in forecast.predictions)
    assert forecast.model_accuracy > 0.7  # Minimum 70% accuracy

async def test_customer_segmentation():
    """Test RFM customer segmentation"""
    segmenter = SegmentationEngine()
    analysis = await segmenter.perform_rfm_analysis(restaurant_id)
    
    assert analysis.total_customers > 0
    assert 'Champions' in analysis.segments
    assert all(score in ['111', '155', '511', '544', '554', '555'] 
              for score in analysis.rfm_data)
```

```bash
# Run tests iteratively until passing:
cd backend
pytest tests/ -v --cov=app --cov-report=term-missing --cov-fail-under=80

cd ../frontend  
npm test -- --coverage --watchAll=false

# If failing: Debug specific test, fix code, re-run
```

### Level 3: Integration Test
```bash
# Test full stack integration
cd backend
python -m uvicorn app.main:app --reload &
BACKEND_PID=$!

cd ../frontend
npm run dev &
FRONTEND_PID=$!

# Wait for services to start
sleep 10

# Test API endpoints
curl -X GET "http://localhost:8000/api/v1/product-intelligence/menu-engineering/performance-matrix?restaurant_id=test-uuid"
# Expected: JSON response with menu classifications

curl -X GET "http://localhost:8000/api/v1/place-intelligence/customer-density/heatmap?restaurant_id=test-uuid"
# Expected: JSON response with heatmap data

curl -X POST "http://localhost:8000/api/v1/price-intelligence/forecasting/generate" \
  -H "Content-Type: application/json" \
  -d '{"restaurant_id": "test-uuid", "days_ahead": 30}'
# Expected: JSON response with revenue forecast

# Test frontend integration
open http://localhost:3000/product-intelligence/menu-engineering
# Expected: Dashboard loads with menu performance matrix

# Cleanup
kill $BACKEND_PID $FRONTEND_PID
```

## Final Validation Checklist
- [ ] All backend tests pass: `pytest tests/ -v --cov-fail-under=80`
- [ ] All frontend tests pass: `npm test -- --coverage`
- [ ] No linting errors: `black . && flake8 . && npm run lint`
- [ ] No type errors: `mypy app/ && npm run type-check`
- [ ] Database migrations run successfully
- [ ] All API endpoints respond correctly
- [ ] Frontend components render without errors
- [ ] Real-time updates work correctly
- [ ] Performance targets met (<3s load time)
- [ ] Security tests pass (authentication, authorization)
- [ ] Mobile responsiveness verified
- [ ] Cross-browser compatibility tested

---

## Anti-Patterns to Avoid
- ❌ Don't ignore existing codebase patterns - follow FastAPI/Next.js conventions
- ❌ Don't skip database migrations - always create proper Alembic migrations
- ❌ Don't hardcode API endpoints - use environment configuration
- ❌ Don't skip error handling - implement comprehensive error boundaries
- ❌ Don't ignore performance - optimize database queries and component rendering
- ❌ Don't skip accessibility - ensure WCAG compliance for all components
- ❌ Don't commit sensitive data - use proper environment variable management
- ❌ Don't skip real-time features - implement WebSocket updates where needed

## Confidence Score: 8.5/10

High confidence due to:
- Strong existing technical foundation (Next.js 15 + FastAPI + PostgreSQL)
- Resolved build issues documented in BUILD_STATUS_REPORT.md
- Clear implementation patterns from existing codebase
- Comprehensive testing strategy and validation gates
- Well-defined business requirements and success metrics

Minor uncertainty areas:
- ML model performance may require iteration and tuning
- Customer data availability for density analysis may vary by restaurant
- Real-time features implementation complexity may require additional optimization