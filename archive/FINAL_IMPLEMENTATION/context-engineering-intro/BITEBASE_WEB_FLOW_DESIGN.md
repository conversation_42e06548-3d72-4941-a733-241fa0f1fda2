# BiteBase Intelligence Web Flow Design
## Complete 4P Framework Implementation Plan

**Version:** 1.0  
**Date:** July 29, 2025  
**Based on:** Original Desk Vision + Current Implementation Analysis  

---

## 🎯 **Web Flow Architecture Overview**

### **Current Navigation Structure Analysis**
```typescript
// Existing Navigation (MainLayout.tsx)
Dashboard
├── Main Dashboard Overview ✅
Intelligence
├── Location Intelligence ✅ 
├── Research Agent ✅
├── Analytics ✅
└── Integrated Analytics ✅
Management
├── Restaurant Management ✅
├── Campaign Management ✅
└── POS Integration ✅
Data & Reports
├── Natural Language Query ✅
├── Insights Dashboard ✅
└── Enhanced Dashboards ✅
```

### **Enhanced 4P Framework Navigation Design**
```typescript
// Proposed Enhanced Navigation Structure
🏠 Dashboard
├── Executive Overview
├── Quick Actions Hub
└── Performance Summary

📊 Product Intelligence (Menu & Sales)
├── Menu Engineering Dashboard
├── Food Cost Analysis
├── Dynamic Pricing Engine
├── Seasonal Trend Analysis
└── Profit Optimization

📍 Place Intelligence (Location & Market)
├── Interactive Market Maps
├── Customer Density Analysis
├── Competitor Intelligence
├── Delivery Hotspot Analysis
└── Site Selection Tools

💰 Price Intelligence (Revenue & Profitability)
├── Revenue Forecasting
├── Peak Performance Analysis
├── Promotion Effectiveness
├── Customer Spending Analytics
└── Financial Planning

📢 Promotion Intelligence (Marketing & Engagement)
├── Customer Segmentation
├── Campaign Performance
├── Sentiment Analysis
├── Loyalty Analytics
└── Marketing Automation

🔧 Operations & Management
├── Restaurant Operations
├── Staff Management
├── Inventory Control
└── POS Integration

🤖 AI & Insights
├── Natural Language Query
├── Automated Insights
├── Predictive Analytics
└── Custom Reports
```

---

## 🏗️ **Detailed Implementation Tasks**

### **Phase 1: Product Intelligence Enhancement**

#### **Task 1.1: Menu Engineering Dashboard**
**Frontend Components:**
```typescript
// New Components to Create
components/product/
├── MenuEngineeringDashboard.tsx     // Main dashboard
├── MenuPerformanceMatrix.tsx        // Star/Dog/Plow Horse/Puzzle grid
├── FoodCostAnalyzer.tsx            // Cost breakdown analysis
├── ProfitMarginOptimizer.tsx       // Margin optimization tools
├── MenuItemPerformanceCard.tsx     // Individual item analytics
└── SeasonalTrendChart.tsx          // Seasonal performance trends

// New Pages to Create
app/product-intelligence/
├── page.tsx                        // Main Product Intelligence page
├── menu-engineering/page.tsx       // Menu Engineering dashboard
├── food-cost-analysis/page.tsx     // Food cost analysis
├── dynamic-pricing/page.tsx        // Dynamic pricing engine
└── seasonal-trends/page.tsx        // Seasonal analysis
```

**Backend API Endpoints:**
```python
# New API Endpoints to Implement
/api/v1/product-intelligence/
├── /menu-engineering/
│   ├── GET /performance-matrix      # Menu item classification
│   ├── GET /profitability-analysis # Profit margin analysis
│   ├── POST /optimize-pricing       # Dynamic pricing suggestions
│   └── GET /seasonal-trends         # Seasonal performance data
├── /food-costs/
│   ├── GET /cost-breakdown         # Detailed cost analysis
│   ├── POST /update-costs          # Update ingredient costs
│   ├── GET /supplier-comparison    # Supplier cost comparison
│   └── GET /cost-trends            # Cost trend analysis
└── /pricing-optimization/
    ├── POST /calculate-optimal     # Calculate optimal pricing
    ├── GET /price-elasticity       # Price elasticity analysis
    └── GET /competitor-pricing     # Competitor price analysis
```

**Database Schema Extensions:**
```sql
-- Food Cost Tracking Tables
CREATE TABLE ingredient_costs (
    id UUID PRIMARY KEY,
    ingredient_name VARCHAR(255) NOT NULL,
    cost_per_unit DECIMAL(10,4),
    unit_type VARCHAR(50),
    supplier_id UUID,
    last_updated TIMESTAMP,
    restaurant_id UUID REFERENCES restaurants(id)
);

CREATE TABLE menu_item_costs (
    id UUID PRIMARY KEY,
    menu_item_id UUID REFERENCES menu_items(id),
    ingredient_cost DECIMAL(10,2),
    labor_cost DECIMAL(10,2),
    overhead_cost DECIMAL(10,2),
    total_cost DECIMAL(10,2),
    profit_margin DECIMAL(5,2),
    classification VARCHAR(20), -- 'star', 'dog', 'plow_horse', 'puzzle'
    created_at TIMESTAMP
);

CREATE TABLE pricing_history (
    id UUID PRIMARY KEY,
    menu_item_id UUID REFERENCES menu_items(id),
    old_price DECIMAL(10,2),
    new_price DECIMAL(10,2),
    reason VARCHAR(255),
    effective_date TIMESTAMP,
    created_by UUID
);
```

#### **Task 1.2: Dynamic Pricing Engine**
**Frontend Components:**
```typescript
components/product/pricing/
├── DynamicPricingDashboard.tsx     // Main pricing dashboard
├── PriceOptimizationPanel.tsx      // Price optimization controls
├── PriceElasticityChart.tsx        // Price elasticity visualization
├── CompetitorPriceComparison.tsx   // Competitor pricing analysis
└── PricingRecommendations.tsx      // AI pricing suggestions
```

**Backend Services:**
```python
# New Services to Implement
app/services/product/
├── pricing_engine.py               # Dynamic pricing algorithms
├── cost_analyzer.py               # Food cost analysis
├── menu_optimizer.py              # Menu optimization logic
└── seasonal_analyzer.py           # Seasonal trend analysis
```

### **Phase 2: Place Intelligence Enhancement**

#### **Task 2.1: Customer Density Heatmaps**
**Frontend Components:**
```typescript
components/place/
├── CustomerDensityMap.tsx          // Interactive heatmap
├── DensityAnalysisPanel.tsx        // Analysis controls
├── HotspotIdentifier.tsx           // Hotspot detection
├── TrafficPatternChart.tsx         // Traffic pattern analysis
└── LocationScoreCard.tsx           // Location scoring
```

**Backend API Endpoints:**
```python
/api/v1/place-intelligence/
├── /customer-density/
│   ├── GET /heatmap-data           # Heatmap visualization data
│   ├── GET /density-analysis       # Density analysis results
│   ├── GET /traffic-patterns       # Traffic pattern data
│   └── GET /hotspot-detection      # Hotspot identification
├── /delivery-analysis/
│   ├── GET /delivery-zones         # Delivery zone analysis
│   ├── GET /pickup-hotspots        # Pickup location analysis
│   └── GET /logistics-optimization # Delivery optimization
└── /site-selection/
    ├── POST /analyze-location      # Location analysis
    ├── GET /market-saturation      # Market saturation analysis
    └── GET /expansion-opportunities # Expansion recommendations
```

#### **Task 2.2: Enhanced Location Intelligence**
**Frontend Components:**
```typescript
components/place/location/
├── InteractiveMarketMap.tsx        // Enhanced map interface
├── CompetitorAnalysisPanel.tsx     // Competitor analysis
├── DemographicOverlay.tsx          // Demographic data overlay
├── MarketSaturationChart.tsx       // Market saturation analysis
└── SiteSelectionWizard.tsx         // Site selection tool
```

### **Phase 3: Price Intelligence Enhancement**

#### **Task 3.1: Revenue Forecasting Engine**
**Frontend Components:**
```typescript
components/price/
├── RevenueForecastDashboard.tsx    // Main forecasting dashboard
├── ForecastingModels.tsx           // ML model selection
├── SeasonalityAnalysis.tsx         // Seasonal pattern analysis
├── ExternalFactorsPanel.tsx        // External factors integration
└── ForecastAccuracyMetrics.tsx     // Model accuracy tracking
```

**Backend Services:**
```python
app/services/price/
├── forecasting_engine.py          # ML-based forecasting
├── revenue_analyzer.py            # Revenue analysis
├── peak_analyzer.py               # Peak performance analysis
└── external_factors.py            # External data integration
```

#### **Task 3.2: Customer Spending Analytics**
**Frontend Components:**
```typescript
components/price/customer/
├── SpendingBehaviorDashboard.tsx   // Customer spending analysis
├── CustomerLifetimeValue.tsx       // CLV calculation and display
├── SpendingPatternChart.tsx        // Spending pattern visualization
├── PriceElasticityAnalysis.tsx     // Price sensitivity analysis
└── RevenueOptimization.tsx         // Revenue optimization tools
```

### **Phase 4: Promotion Intelligence Enhancement**

#### **Task 4.1: Advanced Customer Segmentation**
**Frontend Components:**
```typescript
components/promotion/
├── CustomerSegmentationDashboard.tsx // Main segmentation dashboard
├── RFMAnalysisPanel.tsx             // RFM analysis interface
├── SegmentPerformanceChart.tsx      // Segment performance metrics
├── LoyaltyAnalyticsDashboard.tsx    // Loyalty program analytics
└── PersonalizationEngine.tsx        // Personalization tools
```

**Backend API Endpoints:**
```python
/api/v1/promotion-intelligence/
├── /customer-segmentation/
│   ├── GET /segments               # Customer segments
│   ├── POST /create-segment        # Create custom segment
│   ├── GET /rfm-analysis          # RFM analysis results
│   └── GET /segment-performance    # Segment performance metrics
├── /loyalty-analytics/
│   ├── GET /loyalty-metrics        # Loyalty program metrics
│   ├── GET /retention-analysis     # Customer retention analysis
│   └── GET /lifetime-value         # Customer lifetime value
└── /campaign-automation/
    ├── POST /generate-campaign     # Auto-generate campaigns
    ├── GET /campaign-suggestions   # Campaign recommendations
    └── GET /performance-tracking   # Campaign performance
```

#### **Task 4.2: Sentiment Analysis Enhancement**
**Frontend Components:**
```typescript
components/promotion/sentiment/
├── SentimentAnalysisDashboard.tsx  // Main sentiment dashboard
├── ReviewSentimentChart.tsx        // Review sentiment trends
├── SocialMediaMonitor.tsx          // Social media sentiment
├── CompetitorSentiment.tsx         // Competitor sentiment analysis
└── ResponseRecommendations.tsx     // AI response suggestions
```

---

## 🔄 **User Journey Flow Design**

### **Primary User Flows**

#### **Flow 1: Restaurant Owner Daily Check-in**
```
1. Login → Dashboard Overview
2. Check Performance Summary (KPIs)
3. Review AI Insights & Alerts
4. Navigate to specific area of concern
5. Take action based on recommendations
6. Monitor results in real-time
```

#### **Flow 2: Menu Optimization Workflow**
```
1. Product Intelligence → Menu Engineering
2. Analyze Menu Performance Matrix
3. Identify underperforming items
4. Review Food Cost Analysis
5. Apply Dynamic Pricing Recommendations
6. Monitor impact on profitability
```

#### **Flow 3: Location Analysis Workflow**
```
1. Place Intelligence → Interactive Maps
2. Select location or area of interest
3. Analyze customer density patterns
4. Review competitor landscape
5. Assess market opportunities
6. Generate location report
```

#### **Flow 4: Marketing Campaign Creation**
```
1. Promotion Intelligence → Customer Segmentation
2. Identify target customer segments
3. Analyze segment behavior patterns
4. Create personalized campaign
5. Launch and monitor performance
6. Optimize based on results
```

### **Navigation Enhancement Requirements**

#### **Breadcrumb Navigation**
```typescript
// Enhanced breadcrumb system
Dashboard > Product Intelligence > Menu Engineering > Item Analysis
Dashboard > Place Intelligence > Market Analysis > Competitor Report
Dashboard > Price Intelligence > Revenue Forecasting > Seasonal Trends
Dashboard > Promotion Intelligence > Customer Segmentation > RFM Analysis
```

#### **Quick Action Shortcuts**
```typescript
// Global quick actions available from any page
- Quick Menu Analysis
- Instant Location Check
- Revenue Forecast
- Customer Segment Creation
- AI Insight Generation
```

#### **Context-Aware Sidebar**
```typescript
// Dynamic sidebar based on current section
Product Intelligence Sidebar:
├── Menu Performance
├── Cost Analysis
├── Pricing Tools
└── Seasonal Trends

Place Intelligence Sidebar:
├── Market Maps
├── Competitor Analysis
├── Customer Density
└── Site Selection

// Similar for Price and Promotion sections
```

---

## 📱 **Responsive Design Requirements**

### **Mobile-First Approach**
- Touch-optimized interactions for all components
- Collapsible navigation for mobile devices
- Swipe gestures for chart navigation
- Mobile-specific dashboard layouts

### **Tablet Optimization**
- Split-screen views for comparative analysis
- Drag-and-drop functionality for dashboard customization
- Touch-friendly chart interactions
- Landscape/portrait mode optimization

### **Desktop Enhancement**
- Multi-monitor support for large dashboards
- Keyboard shortcuts for power users
- Advanced filtering and search capabilities
- Detailed tooltips and contextual help

---

## 🎨 **UI/UX Enhancement Specifications**

### **Design System Updates**
```typescript
// Enhanced color palette for 4P framework
const colorPalette = {
  product: '#10B981',    // Green for Product Intelligence
  place: '#3B82F6',      // Blue for Place Intelligence  
  price: '#F59E0B',      // Amber for Price Intelligence
  promotion: '#8B5CF6',  // Purple for Promotion Intelligence
  neutral: '#6B7280',    // Gray for general elements
  success: '#059669',    // Success states
  warning: '#D97706',    // Warning states
  error: '#DC2626'       // Error states
};
```

### **Component Library Extensions**
```typescript
// New component categories
components/
├── product/           // Product Intelligence components
├── place/            // Place Intelligence components  
├── price/            // Price Intelligence components
├── promotion/        // Promotion Intelligence components
├── shared/           // Shared components across 4Ps
└── layouts/          // Enhanced layout components
```

### **Animation and Interaction Patterns**
- Smooth transitions between 4P sections
- Loading states for complex analytics
- Interactive chart animations
- Contextual micro-interactions
- Progressive disclosure patterns

---

## 📋 **Detailed Task Breakdown with Subtasks**

### **PHASE 1: Product Intelligence Implementation**

#### **Epic 1.1: Menu Engineering Dashboard**
**Duration:** 3-4 weeks
**Priority:** High
**Dependencies:** Backend food cost API, Menu data models

**Subtasks:**
1. **Backend Development (Week 1-2)**
   - [ ] Create `ingredient_costs` table schema
   - [ ] Create `menu_item_costs` table schema
   - [ ] Create `pricing_history` table schema
   - [ ] Implement `FoodCostAnalyzer` service
   - [ ] Implement `MenuOptimizer` service
   - [ ] Create API endpoints for menu engineering
   - [ ] Add menu classification algorithms (Star/Dog/Plow Horse/Puzzle)
   - [ ] Implement cost calculation logic
   - [ ] Add unit tests for all services

2. **Frontend Development (Week 2-3)**
   - [ ] Create `MenuEngineeringDashboard.tsx` component
   - [ ] Create `MenuPerformanceMatrix.tsx` component
   - [ ] Create `FoodCostAnalyzer.tsx` component
   - [ ] Create `ProfitMarginOptimizer.tsx` component
   - [ ] Create `MenuItemPerformanceCard.tsx` component
   - [ ] Implement data fetching hooks
   - [ ] Add responsive design for mobile/tablet
   - [ ] Implement real-time updates

3. **Integration & Testing (Week 3-4)**
   - [ ] Connect frontend to backend APIs
   - [ ] Implement error handling and loading states
   - [ ] Add data validation and sanitization
   - [ ] Create comprehensive test suite
   - [ ] Performance optimization
   - [ ] User acceptance testing

#### **Epic 1.2: Dynamic Pricing Engine**
**Duration:** 2-3 weeks
**Priority:** High
**Dependencies:** Menu engineering data, competitor pricing data

**Subtasks:**
1. **Algorithm Development (Week 1)**
   - [ ] Research and implement price elasticity algorithms
   - [ ] Create demand forecasting models
   - [ ] Implement competitor price tracking
   - [ ] Add external factor integration (weather, events)
   - [ ] Create pricing recommendation engine

2. **API Development (Week 1-2)**
   - [ ] Create pricing optimization endpoints
   - [ ] Implement price elasticity analysis API
   - [ ] Add competitor pricing comparison API
   - [ ] Create pricing history tracking
   - [ ] Add bulk pricing update functionality

3. **Frontend Implementation (Week 2-3)**
   - [ ] Create `DynamicPricingDashboard.tsx`
   - [ ] Create `PriceOptimizationPanel.tsx`
   - [ ] Create `PriceElasticityChart.tsx`
   - [ ] Create `CompetitorPriceComparison.tsx`
   - [ ] Create `PricingRecommendations.tsx`
   - [ ] Implement pricing simulation tools
   - [ ] Add pricing approval workflow

### **PHASE 2: Place Intelligence Implementation**

#### **Epic 2.1: Customer Density Heatmaps**
**Duration:** 3-4 weeks
**Priority:** Medium-High
**Dependencies:** Location data, customer data, mapping infrastructure

**Subtasks:**
1. **Data Infrastructure (Week 1)**
   - [ ] Set up geospatial database extensions
   - [ ] Create customer location tracking schema
   - [ ] Implement location data aggregation
   - [ ] Add privacy-compliant data collection
   - [ ] Create data anonymization processes

2. **Backend Services (Week 1-2)**
   - [ ] Implement `CustomerDensityAnalyzer` service
   - [ ] Create heatmap data generation algorithms
   - [ ] Add traffic pattern analysis
   - [ ] Implement hotspot detection algorithms
   - [ ] Create location scoring system

3. **Frontend Mapping (Week 2-3)**
   - [ ] Enhance existing map components
   - [ ] Create `CustomerDensityMap.tsx`
   - [ ] Create `DensityAnalysisPanel.tsx`
   - [ ] Create `HotspotIdentifier.tsx`
   - [ ] Create `TrafficPatternChart.tsx`
   - [ ] Implement interactive heatmap overlays

4. **Integration & Optimization (Week 3-4)**
   - [ ] Optimize map rendering performance
   - [ ] Add real-time data updates
   - [ ] Implement caching strategies
   - [ ] Add export functionality
   - [ ] Create mobile-optimized views

#### **Epic 2.2: Enhanced Site Selection Tools**
**Duration:** 2-3 weeks
**Priority:** Medium
**Dependencies:** Market data, demographic data, competitor data

**Subtasks:**
1. **Site Analysis Engine (Week 1-2)**
   - [ ] Create site scoring algorithms
   - [ ] Implement market saturation analysis
   - [ ] Add demographic matching algorithms
   - [ ] Create expansion opportunity detection
   - [ ] Implement ROI prediction models

2. **Frontend Tools (Week 2-3)**
   - [ ] Create `SiteSelectionWizard.tsx`
   - [ ] Create `MarketSaturationChart.tsx`
   - [ ] Create `DemographicOverlay.tsx`
   - [ ] Add location comparison tools
   - [ ] Implement site recommendation system

### **PHASE 3: Price Intelligence Implementation**

#### **Epic 3.1: Revenue Forecasting Engine**
**Duration:** 4-5 weeks
**Priority:** High
**Dependencies:** Historical sales data, external data sources, ML infrastructure

**Subtasks:**
1. **ML Model Development (Week 1-2)**
   - [ ] Research and select forecasting algorithms
   - [ ] Implement time series forecasting models
   - [ ] Add seasonal decomposition algorithms
   - [ ] Create external factor integration
   - [ ] Implement model validation and testing

2. **Backend Services (Week 2-3)**
   - [ ] Create `ForecastingEngine` service
   - [ ] Implement `RevenueAnalyzer` service
   - [ ] Create `PeakAnalyzer` service
   - [ ] Add `ExternalFactors` integration
   - [ ] Implement model retraining pipeline

3. **Frontend Dashboard (Week 3-4)**
   - [ ] Create `RevenueForecastDashboard.tsx`
   - [ ] Create `ForecastingModels.tsx`
   - [ ] Create `SeasonalityAnalysis.tsx`
   - [ ] Create `ExternalFactorsPanel.tsx`
   - [ ] Create `ForecastAccuracyMetrics.tsx`

4. **Model Optimization (Week 4-5)**
   - [ ] Implement A/B testing for models
   - [ ] Add model performance monitoring
   - [ ] Create automated retraining
   - [ ] Optimize prediction accuracy
   - [ ] Add confidence intervals

#### **Epic 3.2: Customer Spending Analytics**
**Duration:** 2-3 weeks
**Priority:** Medium-High
**Dependencies:** Customer transaction data, segmentation data

**Subtasks:**
1. **Analytics Engine (Week 1-2)**
   - [ ] Implement customer lifetime value calculation
   - [ ] Create spending pattern analysis
   - [ ] Add price elasticity analysis
   - [ ] Implement revenue optimization algorithms
   - [ ] Create customer value segmentation

2. **Frontend Components (Week 2-3)**
   - [ ] Create `SpendingBehaviorDashboard.tsx`
   - [ ] Create `CustomerLifetimeValue.tsx`
   - [ ] Create `SpendingPatternChart.tsx`
   - [ ] Create `PriceElasticityAnalysis.tsx`
   - [ ] Create `RevenueOptimization.tsx`

### **PHASE 4: Promotion Intelligence Implementation**

#### **Epic 4.1: Advanced Customer Segmentation**
**Duration:** 3-4 weeks
**Priority:** High
**Dependencies:** Customer data, transaction history, behavioral data

**Subtasks:**
1. **Segmentation Engine (Week 1-2)**
   - [ ] Implement RFM analysis algorithms
   - [ ] Create behavioral segmentation models
   - [ ] Add demographic segmentation
   - [ ] Implement clustering algorithms
   - [ ] Create segment performance tracking

2. **Backend APIs (Week 2)**
   - [ ] Create customer segmentation endpoints
   - [ ] Implement RFM analysis API
   - [ ] Add segment performance API
   - [ ] Create custom segment creation API
   - [ ] Implement segment export functionality

3. **Frontend Dashboard (Week 2-3)**
   - [ ] Create `CustomerSegmentationDashboard.tsx`
   - [ ] Create `RFMAnalysisPanel.tsx`
   - [ ] Create `SegmentPerformanceChart.tsx`
   - [ ] Create `LoyaltyAnalyticsDashboard.tsx`
   - [ ] Create `PersonalizationEngine.tsx`

4. **Advanced Features (Week 3-4)**
   - [ ] Implement predictive segmentation
   - [ ] Add segment migration tracking
   - [ ] Create automated segment updates
   - [ ] Implement segment-based recommendations
   - [ ] Add segment comparison tools

#### **Epic 4.2: Marketing Automation Engine**
**Duration:** 3-4 weeks
**Priority:** Medium-High
**Dependencies:** Customer segments, campaign data, communication channels

**Subtasks:**
1. **Automation Engine (Week 1-2)**
   - [ ] Create campaign generation algorithms
   - [ ] Implement trigger-based campaigns
   - [ ] Add personalization engine
   - [ ] Create A/B testing framework
   - [ ] Implement campaign optimization

2. **Integration Layer (Week 2-3)**
   - [ ] Integrate with email marketing platforms
   - [ ] Add SMS marketing integration
   - [ ] Create social media posting automation
   - [ ] Implement push notification system
   - [ ] Add campaign tracking and analytics

3. **Frontend Interface (Week 3-4)**
   - [ ] Create campaign builder interface
   - [ ] Add automation workflow designer
   - [ ] Implement campaign performance dashboard
   - [ ] Create A/B testing interface
   - [ ] Add campaign calendar and scheduling

---

## 🔧 **Technical Implementation Specifications**

### **Database Schema Enhancements**

#### **Product Intelligence Tables**
```sql
-- Enhanced menu item tracking
CREATE TABLE menu_item_analytics (
    id UUID PRIMARY KEY,
    menu_item_id UUID REFERENCES menu_items(id),
    date DATE,
    orders_count INTEGER,
    revenue DECIMAL(10,2),
    profit_margin DECIMAL(5,2),
    customer_rating DECIMAL(3,2),
    preparation_time INTEGER,
    return_rate DECIMAL(5,4),
    classification VARCHAR(20)
);

-- Supplier and cost tracking
CREATE TABLE suppliers (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_info JSONB,
    reliability_score DECIMAL(3,2),
    average_delivery_time INTEGER,
    payment_terms VARCHAR(100)
);

-- Price optimization tracking
CREATE TABLE price_optimization_logs (
    id UUID PRIMARY KEY,
    menu_item_id UUID REFERENCES menu_items(id),
    old_price DECIMAL(10,2),
    new_price DECIMAL(10,2),
    optimization_reason TEXT,
    expected_impact JSONB,
    actual_impact JSONB,
    created_at TIMESTAMP
);
```

#### **Place Intelligence Tables**
```sql
-- Customer location and density tracking
CREATE TABLE customer_locations (
    id UUID PRIMARY KEY,
    customer_id UUID,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    visit_frequency INTEGER,
    last_visit TIMESTAMP,
    anonymized_id VARCHAR(255)
);

-- Market analysis results
CREATE TABLE market_analysis_results (
    id UUID PRIMARY KEY,
    analysis_date DATE,
    location_point GEOGRAPHY(POINT),
    radius_km DECIMAL(5,2),
    market_saturation DECIMAL(5,4),
    competition_density INTEGER,
    demographic_match_score DECIMAL(3,2),
    opportunity_score DECIMAL(3,2)
);

-- Site selection scoring
CREATE TABLE site_scores (
    id UUID PRIMARY KEY,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    overall_score DECIMAL(3,2),
    demographic_score DECIMAL(3,2),
    competition_score DECIMAL(3,2),
    accessibility_score DECIMAL(3,2),
    cost_score DECIMAL(3,2),
    analysis_date TIMESTAMP
);
```

#### **Price Intelligence Tables**
```sql
-- Revenue forecasting models
CREATE TABLE forecast_models (
    id UUID PRIMARY KEY,
    restaurant_id UUID REFERENCES restaurants(id),
    model_type VARCHAR(50),
    model_parameters JSONB,
    accuracy_metrics JSONB,
    training_data_period DATERANGE,
    last_trained TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Revenue predictions
CREATE TABLE revenue_forecasts (
    id UUID PRIMARY KEY,
    restaurant_id UUID REFERENCES restaurants(id),
    forecast_date DATE,
    predicted_revenue DECIMAL(12,2),
    confidence_interval_lower DECIMAL(12,2),
    confidence_interval_upper DECIMAL(12,2),
    actual_revenue DECIMAL(12,2),
    model_id UUID REFERENCES forecast_models(id),
    created_at TIMESTAMP
);

-- Customer spending patterns
CREATE TABLE customer_spending_patterns (
    id UUID PRIMARY KEY,
    customer_id UUID,
    restaurant_id UUID REFERENCES restaurants(id),
    average_order_value DECIMAL(10,2),
    visit_frequency DECIMAL(5,2),
    lifetime_value DECIMAL(12,2),
    price_sensitivity DECIMAL(3,2),
    preferred_categories JSONB,
    last_updated TIMESTAMP
);
```

#### **Promotion Intelligence Tables**
```sql
-- Customer segmentation
CREATE TABLE customer_segments (
    id UUID PRIMARY KEY,
    segment_name VARCHAR(255),
    segment_criteria JSONB,
    rfm_score_range JSONB,
    customer_count INTEGER,
    average_clv DECIMAL(12,2),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Customer segment assignments
CREATE TABLE customer_segment_assignments (
    id UUID PRIMARY KEY,
    customer_id UUID,
    segment_id UUID REFERENCES customer_segments(id),
    assignment_date TIMESTAMP,
    confidence_score DECIMAL(3,2),
    is_active BOOLEAN DEFAULT true
);

-- Marketing automation campaigns
CREATE TABLE automated_campaigns (
    id UUID PRIMARY KEY,
    campaign_name VARCHAR(255),
    trigger_conditions JSONB,
    target_segments JSONB,
    message_templates JSONB,
    automation_rules JSONB,
    performance_metrics JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP
);
```

### **API Endpoint Specifications**

#### **Product Intelligence APIs**
```python
# Menu Engineering Endpoints
@router.get("/product-intelligence/menu-engineering/performance-matrix")
async def get_menu_performance_matrix(
    restaurant_id: UUID,
    date_range: Optional[str] = "30d",
    category: Optional[str] = None
) -> MenuPerformanceMatrix

@router.post("/product-intelligence/menu-engineering/optimize-pricing")
async def optimize_menu_pricing(
    restaurant_id: UUID,
    optimization_criteria: PricingCriteria
) -> PricingRecommendations

@router.get("/product-intelligence/food-costs/breakdown")
async def get_food_cost_breakdown(
    restaurant_id: UUID,
    menu_item_id: Optional[UUID] = None,
    include_trends: bool = True
) -> FoodCostBreakdown
```

#### **Place Intelligence APIs**
```python
# Customer Density Endpoints
@router.get("/place-intelligence/customer-density/heatmap")
async def get_customer_density_heatmap(
    restaurant_id: UUID,
    radius_km: float = 5.0,
    time_period: str = "30d",
    granularity: str = "hour"
) -> HeatmapData

@router.get("/place-intelligence/site-selection/analyze")
async def analyze_potential_site(
    latitude: float,
    longitude: float,
    business_type: str,
    target_demographics: Optional[Dict] = None
) -> SiteAnalysisResult
```

#### **Price Intelligence APIs**
```python
# Revenue Forecasting Endpoints
@router.post("/price-intelligence/forecasting/generate")
async def generate_revenue_forecast(
    restaurant_id: UUID,
    forecast_period: int = 30,
    include_external_factors: bool = True
) -> RevenueForecast

@router.get("/price-intelligence/customer-spending/patterns")
async def get_customer_spending_patterns(
    restaurant_id: UUID,
    segment_id: Optional[UUID] = None,
    analysis_period: str = "90d"
) -> SpendingPatterns
```

#### **Promotion Intelligence APIs**
```python
# Customer Segmentation Endpoints
@router.post("/promotion-intelligence/segmentation/create")
async def create_customer_segment(
    restaurant_id: UUID,
    segmentation_criteria: SegmentationCriteria
) -> CustomerSegment

@router.get("/promotion-intelligence/segmentation/rfm-analysis")
async def get_rfm_analysis(
    restaurant_id: UUID,
    recency_days: int = 365,
    include_predictions: bool = True
) -> RFMAnalysisResult

@router.post("/promotion-intelligence/campaigns/auto-generate")
async def auto_generate_campaign(
    restaurant_id: UUID,
    target_segment_id: UUID,
    campaign_objective: str
) -> AutoGeneratedCampaign
```

This comprehensive web flow design ensures the complete implementation of the original BiteBase Intelligence vision while maintaining the sophisticated foundation already built.
