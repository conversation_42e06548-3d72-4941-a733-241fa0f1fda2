---
config:
  layout: elk
---
flowchart TD
    A["🏠 Dashboard Home"] --> B["📊 Product Intelligence"] & C["📍 Place Intelligence"] & D["💰 Price Intelligence"] & E["📢 Promotion Intelligence"]
    B --> B1["Menu Engineering Dashboard"] & B2["Dynamic Pricing Engine"] & B3["Food Cost Analysis"] & B4["Seasonal Trend Analysis"] & B5["Profit Optimization"]
    C --> C1["Customer Density Heatmaps"] & C2["Interactive Market Maps"] & C3["Competitor Intelligence"] & C4["Site Selection Tools"] & C5["Delivery Hotspot Analysis"]
    D --> D1["Revenue Forecasting"] & D2["Customer Spending Analytics"] & D3["Peak Performance Analysis"] & D4["Financial Planning"] & D5["Price Elasticity Analysis"]
    E --> E1["Customer Segmentation"] & E2["Marketing Automation"] & E3["Sentiment Analysis"] & E4["Loyalty Analytics"] & E5["Campaign Performance"]
    B1 --> B1A["Menu Performance Matrix"] & B1B["Star/Dog/Plow Horse Classification"] & B1C["Profitability Analysis"]
    B2 --> B2A["Price Optimization Panel"] & B2B["Competitor Price Tracking"] & B2C["Demand Forecasting"]
    C1 --> C1A["Real-time Heatmaps"] & C1B["Traffic Pattern Analysis"] & C1C["Hotspot Detection"]
    D1 --> D1A["ML Forecasting Models"] & D1B["Seasonal Decomposition"] & D1C["External Factor Integration"]
    E1 --> E1A["RFM Analysis"] & E1B["Behavioral Segmentation"] & E1C["Predictive Classification"]
    style A fill:#f9f,stroke:#333,stroke-width:4px
    style B fill:#90EE90,stroke:#333,stroke-width:2px
    style C fill:#87CEEB,stroke:#333,stroke-width:2px
    style D fill:#FFD700,stroke:#333,stroke-width:2px
    style E fill:#DDA0DD,stroke:#333,stroke-width:2px
