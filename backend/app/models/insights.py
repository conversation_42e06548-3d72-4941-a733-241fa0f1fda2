"""
BiteBase Intelligence Insights Models
Data models for automated insights and anomaly detection
"""

from sqlalchemy import Column, Integer, String, Float, <PERSON>olean, DateTime, Text, JSON, ForeignKey, Enum
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
import enum

from app.core.database import Base


class InsightType(enum.Enum):
    """Types of insights that can be generated"""
    REVENUE_ANOMALY = "revenue_anomaly"
    CUSTOMER_PATTERN_CHANGE = "customer_pattern_change"
    MENU_PERFORMANCE = "menu_performance"
    SEASONAL_TREND = "seasonal_trend"
    LOCATION_COMPARISON = "location_comparison"
    OPERATIONAL_INSIGHT = "operational_insight"


class InsightSeverity(enum.Enum):
    """Severity levels for insights"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class InsightStatus(enum.Enum):
    """Status of insights"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    DISMISSED = "dismissed"


class AnomalyType(enum.Enum):
    """Types of anomalies detected"""
    STATISTICAL_OUTLIER = "statistical_outlier"
    TREND_DEVIATION = "trend_deviation"
    SEASONAL_ANOMALY = "seasonal_anomaly"
    CORRELATION_BREAK = "correlation_break"


class Insight(Base):
    """Core insights generated by the automated insights engine"""
    __tablename__ = "insights"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Basic insight information
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=False)
    insight_type = Column(Enum(InsightType), nullable=False, index=True)
    severity = Column(Enum(InsightSeverity), nullable=False, index=True)
    status = Column(Enum(InsightStatus), default=InsightStatus.ACTIVE, index=True)
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=True, index=True)
    
    # Insight metrics
    confidence_score = Column(Float, nullable=False)  # 0.0 to 1.0
    impact_score = Column(Float, nullable=False)  # 0.0 to 1.0
    urgency_score = Column(Float, nullable=False)  # 0.0 to 1.0
    
    # Data and context
    data_points = Column(JSON, nullable=False)  # Raw data that triggered the insight
    context = Column(JSON, nullable=True)  # Additional context information
    algorithm_metadata = Column(JSON, nullable=True)  # Algorithm-specific metadata
    
    # Natural language explanation
    explanation = Column(Text, nullable=False)  # AI-generated explanation
    recommendations = Column(JSON, nullable=True)  # List of recommended actions
    
    # Time information
    detected_at = Column(DateTime, default=datetime.utcnow, index=True)
    data_period_start = Column(DateTime, nullable=True, index=True)
    data_period_end = Column(DateTime, nullable=True, index=True)
    
    # User interaction
    views_count = Column(Integer, default=0)
    acknowledged_at = Column(DateTime, nullable=True)
    acknowledged_by = Column(String(36), nullable=True)  # User ID
    resolved_at = Column(DateTime, nullable=True)
    resolved_by = Column(String(36), nullable=True)  # User ID
    
    # Feedback and learning
    user_rating = Column(Integer, nullable=True)  # 1-5 rating
    user_feedback = Column(Text, nullable=True)
    false_positive = Column(Boolean, default=False)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="insights")
    anomalies = relationship("Anomaly", back_populates="insight", cascade="all, delete-orphan")
    notifications = relationship("InsightNotification", back_populates="insight", cascade="all, delete-orphan")


class Anomaly(Base):
    """Detected anomalies in restaurant data"""
    __tablename__ = "anomalies"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    insight_id = Column(String(36), ForeignKey("insights.id"), nullable=False, index=True)
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=True, index=True)
    
    # Anomaly details
    anomaly_type = Column(Enum(AnomalyType), nullable=False, index=True)
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Float, nullable=False)
    expected_value = Column(Float, nullable=True)
    deviation_score = Column(Float, nullable=False)  # How far from normal
    
    # Statistical information
    z_score = Column(Float, nullable=True)
    isolation_score = Column(Float, nullable=True)  # From isolation forest
    statistical_significance = Column(Float, nullable=True)
    
    # Time context
    detected_at = Column(DateTime, default=datetime.utcnow, index=True)
    data_timestamp = Column(DateTime, nullable=False, index=True)
    
    # Algorithm details
    detection_algorithm = Column(String(50), nullable=False)
    algorithm_params = Column(JSON, nullable=True)
    
    # Context data
    contributing_factors = Column(JSON, nullable=True)
    related_metrics = Column(JSON, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    insight = relationship("Insight", back_populates="anomalies")
    restaurant = relationship("Restaurant", back_populates="anomalies")


class InsightPattern(Base):
    """Learned patterns for insight generation"""
    __tablename__ = "insight_patterns"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Pattern identification
    pattern_name = Column(String(255), nullable=False, index=True)
    pattern_type = Column(String(50), nullable=False, index=True)
    description = Column(Text, nullable=False)
    
    # Pattern definition
    conditions = Column(JSON, nullable=False)  # Conditions that trigger this pattern
    thresholds = Column(JSON, nullable=False)  # Threshold values
    
    # Performance metrics
    accuracy_score = Column(Float, default=0.0)
    precision_score = Column(Float, default=0.0)
    recall_score = Column(Float, default=0.0)
    false_positive_rate = Column(Float, default=0.0)
    
    # Usage statistics
    times_triggered = Column(Integer, default=0)
    times_confirmed = Column(Integer, default=0)
    times_dismissed = Column(Integer, default=0)
    
    # Learning data
    training_data = Column(JSON, nullable=True)
    last_updated = Column(DateTime, default=datetime.utcnow)
    
    # Status
    is_active = Column(Boolean, default=True)
    confidence_threshold = Column(Float, default=0.7)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class InsightNotification(Base):
    """Notifications sent for insights"""
    __tablename__ = "insight_notifications"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    insight_id = Column(String(36), ForeignKey("insights.id"), nullable=False, index=True)
    
    # Notification details
    recipient_id = Column(String(36), nullable=False, index=True)  # User ID
    notification_type = Column(String(50), nullable=False)  # email, push, webhook
    channel = Column(String(50), nullable=False)  # specific channel/endpoint
    
    # Message content
    subject = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    
    # Delivery status
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    read_at = Column(DateTime, nullable=True)
    
    # Status tracking
    status = Column(String(20), default="pending")  # pending, sent, delivered, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    insight = relationship("Insight", back_populates="notifications")


class InsightFeedback(Base):
    """User feedback on insights for learning"""
    __tablename__ = "insight_feedback"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    insight_id = Column(String(36), ForeignKey("insights.id"), nullable=False, index=True)
    
    # Feedback details
    user_id = Column(String(36), nullable=False, index=True)
    rating = Column(Integer, nullable=False)  # 1-5 scale
    feedback_text = Column(Text, nullable=True)
    
    # Specific feedback categories
    accuracy_rating = Column(Integer, nullable=True)  # 1-5
    usefulness_rating = Column(Integer, nullable=True)  # 1-5
    timeliness_rating = Column(Integer, nullable=True)  # 1-5
    
    # Action taken
    action_taken = Column(String(100), nullable=True)
    action_result = Column(Text, nullable=True)
    
    # Learning flags
    is_false_positive = Column(Boolean, default=False)
    is_duplicate = Column(Boolean, default=False)
    suggested_improvements = Column(JSON, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class InsightMetrics(Base):
    """Performance metrics for the insights engine"""
    __tablename__ = "insight_metrics"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Time period
    date = Column(DateTime, nullable=False, index=True)
    period_type = Column(String(20), nullable=False)  # hourly, daily, weekly
    
    # Generation metrics
    insights_generated = Column(Integer, default=0)
    insights_by_type = Column(JSON, nullable=True)
    insights_by_severity = Column(JSON, nullable=True)
    
    # Performance metrics
    avg_processing_time_ms = Column(Float, nullable=True)
    avg_confidence_score = Column(Float, nullable=True)
    false_positive_rate = Column(Float, nullable=True)
    
    # User engagement
    insights_viewed = Column(Integer, default=0)
    insights_acknowledged = Column(Integer, default=0)
    insights_resolved = Column(Integer, default=0)
    insights_dismissed = Column(Integer, default=0)
    
    # Feedback metrics
    avg_user_rating = Column(Float, nullable=True)
    total_feedback_count = Column(Integer, default=0)
    
    # System metrics
    data_points_processed = Column(Integer, default=0)
    anomalies_detected = Column(Integer, default=0)
    notifications_sent = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Add relationships to existing models
def add_insights_relationships():
    """Add insights relationships to existing models"""
    from app.models.restaurant import Restaurant
    
    # Add insights relationship to Restaurant model
    Restaurant.insights = relationship("Insight", back_populates="restaurant", cascade="all, delete-orphan")
    Restaurant.anomalies = relationship("Anomaly", back_populates="restaurant", cascade="all, delete-orphan")