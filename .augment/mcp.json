{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/workspace/enhancement-bitebase-intelligence"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "tavily": {"command": "npx", "args": ["-y", "tavily-mcp@0.2.3"], "env": {"TAVILY_API_KEY": "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"}}, "google-maps": {"command": "uvx", "args": ["mcp-server-google-maps"], "env": {"GOOGLE_MAPS_API_KEY": "AIzaSyCfG9E3ggBc1ZBkhqTEDSBm0eYp152tMLk "}}, "time": {"command": "uvx", "args": ["mcp-server-time"]}, "cloudflare": {"command": "npx", "args": ["mcp-remote", "https://observability.mcp.cloudflare.com/sse"]}}}