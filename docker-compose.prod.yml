# BiteBase Intelligence - Production Docker Compose
version: '3.8'

services:
  # PostgreSQL Database with Production Settings
  postgres:
    image: postgres:15-alpine
    container_name: bitebase-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./monitoring/postgres-exporter:/etc/postgres-exporter
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    networks:
      - bitebase-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis with Production Configuration
  redis:
    image: redis:7-alpine
    container_name: bitebase-redis-prod
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
    networks:
      - bitebase-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Backend API with Production Settings
  backend:
    image: ghcr.io/${GITHUB_REPOSITORY}/backend:${IMAGE_TAG:-latest}
    container_name: bitebase-backend-prod
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - DEBUG=false
      - CORS_ORIGINS=${CORS_ORIGINS}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      - RATE_LIMIT_ENABLED=true
      - MONITORING_ENABLED=true
      - LOG_LEVEL=INFO
      - WORKERS=4
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    networks:
      - bitebase-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: always
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Frontend with Production Nginx
  frontend:
    image: ghcr.io/${GITHUB_REPOSITORY}/frontend:${IMAGE_TAG:-latest}
    container_name: bitebase-frontend-prod
    environment:
      - BACKEND_URL=http://backend:8000
      - ENVIRONMENT=production
      - APP_VERSION=${APP_VERSION}
    volumes:
      - frontend_logs:/var/log/nginx
      - ./ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - bitebase-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Load Balancer (HAProxy)
  loadbalancer:
    image: haproxy:2.8-alpine
    container_name: bitebase-loadbalancer
    volumes:
      - ./haproxy/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ./ssl:/etc/ssl/certs:ro
    ports:
      - "80:80"
      - "443:443"
      - "8404:8404"  # HAProxy stats
    networks:
      - bitebase-network
    depends_on:
      - frontend
      - backend
    restart: always
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: bitebase-prometheus-prod
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - ./monitoring/prometheus.prod.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - bitebase-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  grafana:
    image: grafana/grafana:latest
    container_name: bitebase-grafana-prod
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_AUTH_ANONYMOUS_ENABLED=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - bitebase-network
    depends_on:
      - prometheus
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Log Aggregation
  loki:
    image: grafana/loki:latest
    container_name: bitebase-loki-prod
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./monitoring/loki/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    networks:
      - bitebase-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  promtail:
    image: grafana/promtail:latest
    container_name: bitebase-promtail-prod
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./monitoring/promtail/promtail-config.yml:/etc/promtail/config.yml:ro
      - backend_logs:/var/log/backend:ro
      - frontend_logs:/var/log/frontend:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - bitebase-network
    depends_on:
      - loki
    restart: always
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: bitebase-backup
    environment:
      - PGPASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    networks:
      - bitebase-network
    depends_on:
      - postgres
    command: >
      sh -c "
        chmod +x /backup.sh &&
        crond -f
      "
    restart: always
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

# Networks
networks:
  bitebase-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  frontend_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
